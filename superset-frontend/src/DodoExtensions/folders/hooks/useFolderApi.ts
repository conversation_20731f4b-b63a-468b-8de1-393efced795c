import { useCallback } from 'react';
import { SupersetClient } from '@superset-ui/core';
import { Entity } from '../components/types';

export interface MoveItemRequest {
  item_id: number;
  item_type: 'folder' | 'dashboard';
  target_folder_id: number | null;
}

export interface CreateFolderRequest {
  name: string;
  name_ru?: string;
  name_en?: string;
  description?: string;
  description_ru?: string;
  description_en?: string;
  parent_id?: number | null;
  is_global?: boolean;
}

export interface UpdateFolderRequest extends Partial<CreateFolderRequest> {
  id: number;
}

export interface FolderTreeResponse {
  result: Entity[];
}

export const useFolderApi = () => {
  const getFolderTree = useCallback(
    async (type: 'global' | 'personal' | 'team' | 'all' = 'all', locale = 'en'): Promise<Entity[]> => {
      try {
        const response = await SupersetClient.get({
          endpoint: `/api/v1/folder/tree?type=${type}&locale=${locale}`,
        });
        return response.json.result;
      } catch (error) {
        console.error('Error fetching folder tree:', error);
        throw error;
      }
    },
    [],
  );

  const createFolder = useCallback(async (data: CreateFolderRequest) => {
    try {
      const response = await SupersetClient.post({
        endpoint: '/api/v1/folder/',
        jsonPayload: data,
      });
      return response.json;
    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  }, []);

  const updateFolder = useCallback(async (id: number, data: Partial<CreateFolderRequest>) => {
    try {
      const response = await SupersetClient.put({
        endpoint: `/api/v1/folder/${id}`,
        jsonPayload: data,
      });
      return response.json;
    } catch (error) {
      console.error('Error updating folder:', error);
      throw error;
    }
  }, []);

  const deleteFolder = useCallback(async (id: number) => {
    try {
      const response = await SupersetClient.delete({
        endpoint: `/api/v1/folder/${id}`,
      });
      return response.json;
    } catch (error) {
      console.error('Error deleting folder:', error);
      throw error;
    }
  }, []);

  const moveItem = useCallback(async (data: MoveItemRequest) => {
    try {
      const response = await SupersetClient.post({
        endpoint: '/api/v1/folder/move_item',
        jsonPayload: data,
      });
      return response.json;
    } catch (error) {
      console.error('Error moving item:', error);
      throw error;
    }
  }, []);

  const deleteDashboard = useCallback(async (id: number) => {
    try {
      const response = await SupersetClient.delete({
        endpoint: `/api/v1/dashboard/${id}`,
      });
      return response.json;
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      throw error;
    }
  }, []);

  return {
    getFolderTree,
    createFolder,
    updateFolder,
    deleteFolder,
    deleteDashboard,
    moveItem,
  };
};

export default useFolderApi;
