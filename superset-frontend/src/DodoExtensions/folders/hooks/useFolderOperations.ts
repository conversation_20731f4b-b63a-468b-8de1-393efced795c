import { useCallback, useState } from 'react';
import { Entity } from '../components/types';
import { useFolderApi } from './useFolderApi';

export const useFolderOperations = (
  data: Entity[],
  onDataChange: (newData: Entity[]) => void,
  onSuccess?: (message: string) => void,
  onError?: (message: string) => void,
) => {
  const [renameFolderModalVisible, setRenameFolderModalVisible] = useState(false);
  const [addFolderModalVisible, setAddFolderModalVisible] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<Entity | null>(null);
  
  const { updateFolder, createFolder, deleteFolder } = useFolderApi();

  const updateFolderInData = useCallback((entities: Entity[], updatedFolder: Entity): Entity[] => {
    return entities.map(entity => {
      if (entity.id === updatedFolder.id && entity.type === 'folder') {
        return updatedFolder;
      }
      if (entity.type === 'folder' && entity.children) {
        return {
          ...entity,
          children: updateFolderInData(entity.children, updatedFolder),
        };
      }
      return entity;
    });
  }, []);

  const addFolderToData = useCallback((entities: Entity[], folder: Entity): Entity[] => {
    if (folder.parent === '0') {
      return [...entities, folder];
    }

    return entities.map(entity => {
      if (entity.id.toString() === folder.parent && entity.type === 'folder') {
        return {
          ...entity,
          children: [...(entity.children || []), folder],
        };
      }
      if (entity.type === 'folder' && entity.children) {
        return {
          ...entity,
          children: addFolderToData(entity.children, folder),
        };
      }
      return entity;
    });
  }, []);

  const removeFolderFromData = useCallback((entities: Entity[], folderId: number): Entity[] => {
    return entities
      .filter(entity => entity.id !== folderId)
      .map(entity => {
        if (entity.type === 'folder' && entity.children) {
          return {
            ...entity,
            children: removeFolderFromData(entity.children, folderId),
          };
        }
        return entity;
      });
  }, []);

  const handleRenameFolder = useCallback((folder: Entity) => {
    setSelectedFolder(folder);
    setRenameFolderModalVisible(true);
  }, []);

  const handleDeleteFolder = useCallback(async (folder: Entity) => {
    try {
      await deleteFolder(folder.id);
      const newData = removeFolderFromData(data, folder.id);
      onDataChange(newData);
      onSuccess?.('Folder deleted successfully');
    } catch (error) {
      onError?.('Failed to delete folder');
    }
  }, [data, deleteFolder, removeFolderFromData, onDataChange, onSuccess, onError]);

  const handleAddFolder = useCallback(() => {
    setAddFolderModalVisible(true);
  }, []);

  const handleRenameFolderSuccess = useCallback((updatedFolder: Entity) => {
    const newData = updateFolderInData(data, updatedFolder);
    onDataChange(newData);
    setRenameFolderModalVisible(false);
    setSelectedFolder(null);
  }, [data, updateFolderInData, onDataChange]);

  const handleAddFolderSuccess = useCallback((newFolder: Entity) => {
    const newData = addFolderToData(data, newFolder);
    onDataChange(newData);
    setAddFolderModalVisible(false);
  }, [data, addFolderToData, onDataChange]);

  const handleRenameFolderCancel = useCallback(() => {
    setRenameFolderModalVisible(false);
    setSelectedFolder(null);
  }, []);

  const handleAddFolderCancel = useCallback(() => {
    setAddFolderModalVisible(false);
  }, []);

  return {
    // State
    renameFolderModalVisible,
    addFolderModalVisible,
    selectedFolder,
    
    // Handlers
    handleRenameFolder,
    handleDeleteFolder,
    handleAddFolder,
    handleRenameFolderSuccess,
    handleAddFolderSuccess,
    handleRenameFolderCancel,
    handleAddFolderCancel,
  };
};
