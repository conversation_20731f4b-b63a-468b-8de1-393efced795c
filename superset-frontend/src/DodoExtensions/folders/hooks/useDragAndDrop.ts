import { useCallback, useState } from 'react';
import { TreeProps } from 'antd-v5';
import { Entity, TreeNodeData } from '../components/types';
import { useFolderApi } from './useFolderApi';

interface DragInfo {
  event: React.DragEvent<HTMLDivElement>;
  node: TreeNodeData;
}

interface DropInfo {
  event: React.DragEvent<HTMLDivElement>;
  node: TreeNodeData;
  dragNode: TreeNodeData;
  dragNodesKeys: React.Key[];
  dropPosition: number;
  dropToGap: boolean;
}

export const useDragAndDrop = (
  data: Entity[],
  onDataChange: (newData: Entity[]) => void,
  onSuccess?: (message: string) => void,
  onError?: (message: string) => void,
) => {
  const [draggedNode, setDraggedNode] = useState<TreeNodeData | null>(null);
  const { moveItem } = useFolderApi();

  const onDragStart: TreeProps['onDragStart'] = useCallback(
    (info: DragInfo) => {
      setDraggedNode(info.node);
    },
    [],
  );

  const onDragEnd: TreeProps['onDragEnd'] = useCallback(() => {
    setDraggedNode(null);
  }, []);

  const findEntityById = useCallback(
    (entities: Entity[], id: number): Entity | null => {
      for (const entity of entities) {
        if (entity.id === id) {
          return entity;
        }
        if (entity.type === 'folder' && entity.children) {
          const found = findEntityById(entity.children, id);
          if (found) return found;
        }
      }
      return null;
    },
    [],
  );

  const removeEntityById = useCallback(
    (entities: Entity[], id: number): Entity[] =>
      entities
        .filter(entity => entity.id !== id)
        .map(entity => {
          if (entity.type === 'folder' && entity.children) {
            return {
              ...entity,
              children: removeEntityById(entity.children, id),
            };
          }
          return entity;
        }),
    [],
  );

  const addEntityToParent = useCallback(
    (entities: Entity[], entity: Entity, parentId: number | null): Entity[] => {
      if (parentId === null || parentId === 0) {
        // Add to root level
        return [...entities, entity];
      }

      return entities.map(item => {
        if (item.id === parentId && item.type === 'folder') {
          return {
            ...item,
            children: [...(item.children || []), entity],
          };
        }
        if (item.type === 'folder' && item.children) {
          return {
            ...item,
            children: addEntityToParent(item.children, entity, parentId),
          };
        }
        return item;
      });
    },
    [],
  );

  const onDrop: TreeProps['onDrop'] = useCallback(
    async (info: DropInfo) => {
      const { dragNode, node, dropToGap, dropPosition } = info;

      if (!draggedNode) return;

      try {
        // Determine target folder
        let targetFolderId: number | null = null;

        if (!dropToGap) {
          // Dropped on a node
          if (node.type === 'folder') {
            targetFolderId = node.id;
          } else {
            // Can't drop on a dashboard, ignore
            return;
          }
        } else {
          // Dropped between nodes
          // Find the parent of the drop position
          const nodeKey = node.key;
          const parentMatch = nodeKey.match(/^(.+)-\d+$/);
          if (parentMatch) {
            const parentKey = parentMatch[1];
            const parentIdMatch = parentKey.match(/folder-(\d+)$/);
            if (parentIdMatch) {
              targetFolderId = parseInt(parentIdMatch[1], 10);
            }
          }
          // If no parent found, it's root level (targetFolderId remains null)
        }

        // Call API to move item
        await moveItem({
          item_id: dragNode.id,
          item_type: dragNode.type,
          target_folder_id: targetFolderId,
        });

        // Update local state
        const draggedEntity = findEntityById(data, dragNode.id);
        if (draggedEntity) {
          // Remove from current position
          let newData = removeEntityById(data, dragNode.id);

          // Update parent reference
          const updatedEntity = {
            ...draggedEntity,
            parent: targetFolderId ? targetFolderId.toString() : '0',
          };

          // Add to new position
          newData = addEntityToParent(newData, updatedEntity, targetFolderId);

          onDataChange(newData);
          onSuccess?.('Item moved successfully');
        }
      } catch (error) {
        console.error('Error moving item:', error);
        onError?.('Failed to move item');
      }
    },
    [
      draggedNode,
      data,
      moveItem,
      findEntityById,
      removeEntityById,
      addEntityToParent,
      onDataChange,
      onSuccess,
      onError,
    ],
  );

  const allowDrop: TreeProps['allowDrop'] = useCallback(
    ({ dragNode, dropNode, dropPosition }) => {
      // Don't allow dropping on dashboards
      if (dropNode.type === 'dashboard' && dropPosition === 0) {
        return false;
      }

      // Don't allow dropping a folder into itself or its descendants
      if (dragNode.type === 'folder' && dropNode.type === 'folder') {
        const isDescendant = (parentKey: string, childKey: string): boolean =>
          childKey.startsWith(`${parentKey}-`);

        if (
          dropNode.key === dragNode.key ||
          isDescendant(dragNode.key, dropNode.key)
        ) {
          return false;
        }
      }

      return true;
    },
    [],
  );

  return {
    onDragStart,
    onDragEnd,
    onDrop,
    allowDrop,
    draggedNode,
  };
};
