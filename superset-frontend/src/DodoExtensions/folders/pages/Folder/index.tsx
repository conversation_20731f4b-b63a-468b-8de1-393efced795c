import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import SubMenu from 'src/features/home/<USER>';
import FolderCard from '../../components/FolderCard';
import { Entity } from '../../components/types';

const StyledContainer = styled.div`
  padding-inline: ${({ theme }) => theme.gridUnit * 4}px;
  padding-bottom: ${({ theme }) => theme.gridUnit * 4}px;
  display: grid;
  grid-template-columns: 1fr 24px 2fr;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  flex: 1;
`;

const TransferButtonWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border: solid 1px ${({ theme }) => theme.colors.primary.base};
    border-radius: ${({ theme }) => theme.gridUnit}px;
    color: ${({ theme }) => theme.colors.primary.base};

    span {
      height: 14px;
    }
  }
`;

const DashboardList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
`;

const mock: Entity[] = [
  {
    id: 1,
    title_ru: 'Глобальная папка 1',
    title_en: 'Global folder 1',
    description_ru: 'Описание глобальной папки 1',
    description_en: 'Description of global folder 1',
    parent: '0',
    type: 'folder',
    children: [
      {
        id: 11,
        title_ru: 'Аналитика',
        title_en: 'Analytics',
        description_ru: 'Папка с аналитическими отчётами',
        description_en: 'Folder with analytics reports',
        parent: '1',
        type: 'folder',
        children: [
          {
            id: 111,
            title_ru: 'Продажи Q1',
            title_en: 'Sales Q1',
            parent: '1-1',
            type: 'dashboard',
            is_certified: true,
          },
          {
            id: 112,
            title_ru: 'Маркетинг',
            title_en: 'Marketing',
            parent: '1-1',
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
      {
        id: 12,
        title_ru: 'Финансы',
        title_en: 'Finance',
        description_ru: 'Бюджеты и отчётность',
        description_en: 'Budgets and reporting',
        parent: '1',
        type: 'folder',
        children: [
          {
            id: 121,
            title_ru: 'Бюджет 2024',
            title_en: 'Budget 2024',
            parent: '1-2',
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
      {
        id: 13,
        title_ru: 'HR и подбор',
        title_en: 'HR & Recruitment',
        description_ru: 'Отчёты по найму и вовлечённости',
        description_en: 'Reports on hiring and engagement',
        parent: '1',
        type: 'folder',
        children: [
          {
            id: 131,
            title_ru: 'Вакансии',
            title_en: 'Jobs',
            parent: '1-3',
            type: 'dashboard',
            is_certified: false,
          },
        ],
      },
    ],
  },
];

const FolderPage = () => (
  <>
    <SubMenu name={t('Folder')} />
    <StyledContainer>
      <FolderCard title={t('Folder')} to="/folder/0" editMode data={mock} />
      <TransferButtonWrapper>
        <button type="button">
          <Icons.LeftOutlined iconSize="m" />
        </button>
      </TransferButtonWrapper>
      <DashboardList />
    </StyledContainer>
  </>
);

export default FolderPage;
