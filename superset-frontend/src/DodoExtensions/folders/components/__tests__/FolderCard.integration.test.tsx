import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, supersetTheme } from '@superset-ui/core';
import FolderCard from '../FolderCard';
import { Entity } from '../types';

// Mock the hooks
jest.mock('../../hooks/useDragAndDrop', () => ({
  useDragAndDrop: () => ({
    onDragStart: jest.fn(),
    onDragEnd: jest.fn(),
    onDrop: jest.fn(),
    allowDrop: jest.fn(() => true),
    draggedNode: null,
  }),
}));

const mockData: Entity[] = [
  {
    id: 1,
    type: 'folder',
    title_ru: 'Тестовая папка',
    title_en: 'Test Folder',
    description_ru: 'Описание папки',
    description_en: 'Folder description',
    parent: '0',
    children: [
      {
        id: 2,
        type: 'dashboard',
        title_ru: 'Дашборд 1',
        title_en: 'Dashboard 1',
        is_certified: false,
        parent: '1',
      },
    ],
  },
  {
    id: 3,
    type: 'dashboard',
    title_ru: 'Тестовый дашборд',
    title_en: 'Test Dashboard',
    is_certified: true,
    parent: '0',
  },
];

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={supersetTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('FolderCard Integration', () => {
  it('should render FolderCard in view mode', () => {
    const { container } = renderWithTheme(
      <FolderCard
        title="Test Folders"
        to="/dashboard/list/"
        canEdit={false}
        data={mockData}
        editMode={false}
      />
    );

    expect(container).toBeInTheDocument();
    expect(screen.getByText('Test Folders')).toBeInTheDocument();
  });

  it('should render FolderCard in edit mode', () => {
    const { container } = renderWithTheme(
      <FolderCard
        title="Test Folders"
        to="/dashboard/list/"
        canEdit={true}
        data={mockData}
        editMode={true}
      />
    );

    expect(container).toBeInTheDocument();
    expect(screen.getByText('Test Folders')).toBeInTheDocument();
  });

  it('should show search input', () => {
    renderWithTheme(
      <FolderCard
        title="Test Folders"
        to="/dashboard/list/"
        canEdit={false}
        data={mockData}
        editMode={false}
      />
    );

    const searchInput = screen.getByPlaceholderText('Search');
    expect(searchInput).toBeInTheDocument();
  });

  it('should filter data when searching', () => {
    renderWithTheme(
      <FolderCard
        title="Test Folders"
        to="/dashboard/list/"
        canEdit={false}
        data={mockData}
        editMode={false}
      />
    );

    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'Test Folder' } });

    expect(searchInput).toHaveValue('Test Folder');
  });

  it('should show action buttons in edit mode', () => {
    renderWithTheme(
      <FolderCard
        title="Test Folders"
        to="/dashboard/list/"
        canEdit={true}
        data={mockData}
        editMode={true}
      />
    );

    // Check for add folder and save buttons
    const addButton = screen.getByLabelText('Add folder');
    const saveButton = screen.getByLabelText('Save');
    
    expect(addButton).toBeInTheDocument();
    expect(saveButton).toBeInTheDocument();
  });
});
