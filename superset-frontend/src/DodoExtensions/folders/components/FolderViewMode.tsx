import { useState } from 'react';
import cx from 'classnames';
import { styled, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import FolderCard from './FolderCard';
import { FoldersData } from './types';

const mockedData: FoldersData = {
  global: [
    {
      id: 1,
      title_ru: 'Глобальная папка 1',
      title_en: 'Global folder 1',
      description_ru: 'Описание глобальной папки 1',
      description_en: 'Description of global folder 1',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 11,
          title_ru: 'Аналитика',
          title_en: 'Analytics',
          description_ru: 'Папка с аналитическими отчётами',
          description_en: 'Folder with analytics reports',
          parent: '1',
          type: 'folder',
          children: [
            {
              id: 111,
              title_ru: 'Продажи Q1',
              title_en: 'Sales Q1',
              parent: '1-1',
              type: 'dashboard',
              is_certified: true,
            },
            {
              id: 112,
              title_ru: 'Маркетинг',
              title_en: 'Marketing',
              parent: '1-1',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
        {
          id: 12,
          title_ru: 'Финансы',
          title_en: 'Finance',
          description_ru: 'Бюджеты и отчётность',
          description_en: 'Budgets and reporting',
          parent: '1',
          type: 'folder',
          children: [
            {
              id: 121,
              title_ru: 'Бюджет 2024',
              title_en: 'Budget 2024',
              parent: '1-2',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
        {
          id: 13,
          title_ru: 'HR и подбор',
          title_en: 'HR & Recruitment',
          description_ru: 'Отчёты по найму и вовлечённости',
          description_en: 'Reports on hiring and engagement',
          parent: '1',
          type: 'folder',
          children: [
            {
              id: 131,
              title_ru: 'Вакансии',
              title_en: 'Jobs',
              parent: '1-3',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
      ],
    },
    {
      id: 2,
      title_ru: 'Глобальная папка 2',
      title_en: 'Global folder 2',
      description_ru: 'Описание глобальной папки 2',
      description_en: 'Description of global folder 2',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 21,
          title_ru: 'Техническая документация',
          title_en: 'Tech Docs',
          description_ru: 'API, архитектура, инструкции',
          description_en: 'API, architecture, guides',
          parent: '2',
          type: 'folder',
          children: [
            {
              id: 211,
              title_ru: 'Системная архитектура',
              title_en: 'System Architecture',
              parent: '2-1',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
      ],
    },
    {
      id: 3,
      title_ru: 'Общие отчёты',
      title_en: 'Shared Reports',
      description_ru: 'Стандартные отчёты для всех отделов',
      description_en: 'Standard reports for all departments',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 31,
          title_ru: 'Продажи',
          title_en: 'Sales',
          parent: '3',
          type: 'dashboard',
          is_certified: false,
        },
      ],
    },
  ],
  personal: [
    {
      id: 4,
      title_ru: 'Мои проекты',
      title_en: 'My Projects',
      description_ru: 'Личные проекты и задачи',
      description_en: 'Personal projects and tasks',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 41,
          title_ru: 'Проект "Реструктуризация"',
          title_en: 'Project "Restructuring"',
          description_ru: 'Анализ и KPI изменений',
          description_en: 'Analysis and KPI of changes',
          parent: '4',
          type: 'folder',
          children: [
            {
              id: 411,
              title_ru: 'KPI команды',
              title_en: 'Team KPI',
              parent: '4-1',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
        {
          id: 42,
          title_ru: 'Черновики',
          title_en: 'Drafts',
          description_ru: 'Временные и тестовые дашборды',
          description_en: 'Temporary and test dashboards',
          parent: '4',
          type: 'folder',
          children: [
            {
              id: 421,
              title_ru: 'Тестовый дашборд',
              title_en: 'Test Dashboard',
              parent: '4-2',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
      ],
    },
    {
      id: 5,
      title_ru: 'Избранное',
      title_en: 'Favorites',
      description_ru: 'Часто используемые дашборды',
      description_en: 'Frequently used dashboards',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 51,
          title_ru: 'Ежедневный обзор',
          title_en: 'Daily Overview',
          parent: '5',
          type: 'dashboard',
          is_certified: false,
        },
      ],
    },
  ],
  team: [
    {
      id: 6,
      title_ru: 'Команда разработки',
      title_en: 'Dev Team',
      description_ru: 'Отчёты и метрики команды разработки',
      description_en: 'Reports and metrics for dev team',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 61,
          title_ru: 'CI/CD статистика',
          title_en: 'CI/CD Stats',
          parent: '6',
          type: 'dashboard',
          is_certified: false,
        },
        {
          id: 62,
          title_ru: 'Баг-трекинг',
          title_en: 'Bug Tracking',
          parent: '6',
          type: 'dashboard',
          is_certified: false,
        },
      ],
    },
    {
      id: 7,
      title_ru: 'Маркетинговая команда',
      title_en: 'Marketing Team',
      description_ru: 'Кампании, конверсии, ROI',
      description_en: 'Campaigns, conversions, ROI',
      parent: '0',
      type: 'folder',
      children: [
        {
          id: 71,
          title_ru: 'Google Ads',
          title_en: 'Google Ads',
          parent: '7',
          type: 'dashboard',
          is_certified: false,
        },
        {
          id: 72,
          title_ru: 'Email-маркетинг',
          title_en: 'Email Marketing',
          parent: '7',
          type: 'dashboard',
          is_certified: false,
        },
        {
          id: 73,
          title_ru: 'Социальные сети',
          title_en: 'Social Media',
          description_ru: 'Вовлечённость и охват',
          description_en: 'Engagement and reach',
          parent: '7',
          type: 'folder',
          children: [
            {
              id: 731,
              title_ru: 'Instagram',
              title_en: 'Instagram',
              parent: '7-3',
              type: 'dashboard',
              is_certified: false,
            },
            {
              id: 732,
              title_ru: 'LinkedIn',
              title_en: 'LinkedIn',
              parent: '7-3',
              type: 'dashboard',
              is_certified: false,
            },
          ],
        },
      ],
    },
  ],
};

const StyledContainer = styled.div`
  margin-inline: ${({ theme }) => theme.gridUnit * 4}px;
  display: flex;
  gap: ${({ theme }) => theme.gridUnit * 4}px;
  flex: 1;

  .cards-container {
    margin-block: ${({ theme }) => theme.gridUnit * 5 + 1}px;
    display: grid;
    grid-template-columns: repeat(3, minmax(250px, 1fr));
    gap: ${({ theme }) => theme.gridUnit * 4}px;
    flex: 1;
  }
`;

const ViewModeContainer = styled.div`
  padding-right: ${({ theme }) => theme.gridUnit * 4}px;
  margin-top: ${({ theme }) => theme.gridUnit * 5 + 1}px;
  white-space: nowrap;
  display: inline-block;

  .toggle-button {
    display: inline-block;
    border-radius: ${({ theme }) => theme.gridUnit / 2}px;
    padding: ${({ theme }) => theme.gridUnit}px;
    padding-bottom: ${({ theme }) => theme.gridUnit * 0.5}px;

    &:first-of-type {
      margin-right: ${({ theme }) => theme.gridUnit * 2}px;
    }
  }

  .active {
    background-color: ${({ theme }) => theme.colors.grayscale.base};
    svg {
      color: ${({ theme }) => theme.colors.grayscale.light5};
    }
  }
`;

const ViewModeToggle = ({
  setFolderMode,
}: {
  setFolderMode: (value: boolean) => void;
}) => (
  <ViewModeContainer>
    <div
      role="button"
      tabIndex={0}
      className={cx('toggle-button', { active: true })}
    >
      <Icons.FolderOpenOutlined iconSize="l" />
    </div>
    <div
      role="button"
      tabIndex={0}
      onClick={e => {
        e.currentTarget.blur();
        setFolderMode(false);
      }}
      className={cx('toggle-button')}
    >
      <Icons.ListView />
    </div>
  </ViewModeContainer>
);

interface IProps {
  setFolderMode: (value: boolean) => void;
}

const Folders = ({ setFolderMode }: IProps) => {
  const [data] = useState<FoldersData>(mockedData);
  return (
    <StyledContainer>
      <ViewModeToggle setFolderMode={setFolderMode} />
      <div className="cards-container">
        <FolderCard
          title={t('Global folders')}
          to="/dashboard/list/"
          canEdit={false}
          data={data.global}
          editMode={false}
        />
        <FolderCard
          title={t('Personal folders')}
          to="/dashboard/list/"
          canEdit
          data={data.personal}
          editMode={false}
        />
        {data.team && (
          <FolderCard
            title={t('Team folders')}
            to="/dashboard/list/"
            canEdit
            data={data.team}
            editMode={false}
          />
        )}
      </div>
    </StyledContainer>
  );
};

export default Folders;
