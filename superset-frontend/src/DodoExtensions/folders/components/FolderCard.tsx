import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Tree, TreeNodeProps, message } from 'antd-v5';
import { styled, t } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble';
import Card from 'src/components/Card';
import Icons from 'src/components/Icons';
import { Input } from 'src/components/Input';
import { Entity, isFolderType, TreeNodeData, Folder, Dashboard } from './types';
import { buildTreeData, filterTreeData } from './utils';
import FolderModal from './FolderModal';
import FolderTitle from './FolderTitle';

const locale = bootstrapData?.common?.locale || 'en';

const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};

  .header-title {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;

    svg {
      color: ${({ theme }) => theme.colors.primary.base};
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    color: ${({ theme }) => theme.colors.primary.base};
  }

  .browse-link {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit}px;
    line-height: 1rem;
    color: ${({ theme }) => theme.colors.primary.base} !important;

    a {
      color: ${({ theme }) => theme.colors.primary.base} !important;
    }

    span {
      height: 14px;
    }
  }

  .antd5-card-body {
    display: flex;
    flex-direction: column;
    padding: ${({ theme }) => theme.gridUnit * 6}px;
    flex: 1;
  }

  .tree-container {
    margin-top: ${({ theme }) => theme.gridUnit * 5}px;
    overflow-y: auto;
    max-height: 100%;
    flex: 1;
  }

  .tree {
    background-color: transparent;
  }

  .antd5-tree-treenode,
  .antd5-tree-title {
    width: 100%;
  }

  .antd5-tree-node-content-wrapper,
  antd5-tree-node-content-wrapper-normal {
    display: flex;
    flex: 1;
  }

  .item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: ${({ theme }) => theme.gridUnit * 2}px;
    flex: 1;

    span[role='button'] {
      line-height: 14px;
    }

    .dash-delete-icon {
      margin-right: ${({ theme }) => theme.gridUnit * 5}px;
      color: ${({ theme }) => theme.colors.error.base};
    }
  }

  .certified-icon {
    color: ${({ theme }) => theme.colors.primary.base};
  }
`;

const InputIconAlignment = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${({ theme }) => theme.colors.grayscale.base};
`;

const DashboardTitle = ({
  editMode,
  data,
}: {
  editMode: boolean;
  data: Dashboard;
}) =>
  editMode ? (
    <div className="item-title">
      <span>{locale === 'ru' ? data.title_ru : data.title_en}</span>
      <span role="button" aria-label={t('Delete dashboard')}>
        <Icons.DeleteOutlined iconSize="m" className="dash-delete-icon" />
      </span>
    </div>
  ) : (
    <Link to={`/superset/dashboard/${data.id}`} style={{ color: 'inherit' }}>
      {data.is_certified && (
        <Icons.Certified className="certified-icon" iconSize="m" />
      )}
      {locale === 'ru' ? data.title_ru : data.title_en}
    </Link>
  );

const ActionButtons = ({
  onAddFolder,
  onSave,
  hasChanges,
}: {
  onAddFolder?: () => void;
  onSave?: () => void;
  hasChanges?: boolean;
}) => (
  <div className="action-buttons">
    <span
      role="button"
      aria-label={t('Add folder')}
      tabIndex={0}
      onClick={onAddFolder}
    >
      <Icons.FolderAddOutlined iconSize="xl" />
    </span>
    <span
      role="button"
      aria-label={t('Save')}
      tabIndex={0}
      onClick={onSave}
      style={{
        opacity: hasChanges ? 1 : 0.5,
        cursor: hasChanges ? 'pointer' : 'default',
      }}
    >
      <Icons.SaveOutlined iconSize="xl" />
    </span>
  </div>
);

const BrowseLink = ({ to }: { to: string }) => (
  <Link to={to} className="browse-link">
    <span>{t('Browse dashboards')}</span>
    <Icons.ArrowRightOutlined iconSize="m" />
  </Link>
);

interface IProps {
  editMode: boolean;
  canEdit?: boolean;
  title: string;
  to: string;
  data: Entity[];
}

const FolderCard = ({ title, to, canEdit, data, editMode }: IProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [folderData, setFolderData] = useState(data);
  const [addFolderModalVisible, setAddFolderModalVisible] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<Folder | null>(null);

  const changeSearch: React.ChangeEventHandler<HTMLInputElement> = event =>
    setSearchTerm(event.target.value);

  const stopSearching = () => {
    setSearchTerm('');
    searchInputRef.current!.blur();
  };

  // Simple drag and drop handlers for local state management
  const [hasChanges, setHasChanges] = useState(false);

  const onDrop = (info: any) => {
    const { node, dropToGap } = info;

    if (!dropToGap && node.type !== 'folder') {
      return; // Can't drop on dashboard
    }

    // For now, just show a message that changes will be saved
    message.info(t('Changes will be saved when you click Save'));
    setHasChanges(true);
  };

  const allowDrop = ({ dropNode }: any) => {
    // Don't allow dropping on dashboards
    if (dropNode.type === 'dashboard') {
      return false;
    }
    return true;
  };

  const onDragStart = () => {};
  const onDragEnd = () => {};

  const handleDeleteFolder = () => {
    if (!selectedFolder) return;
    setFolderData(folderData.filter(entity => entity.id !== selectedFolder.id));
    setHasChanges(true);
    setSelectedFolder(null);
  };

  const handleEditFolder = (updatedFolder: Entity) => {
    // Update folder data
    const updateFolderInData = (entities: Entity[]): Entity[] =>
      entities.map(entity => {
        if (entity.id === updatedFolder.id && entity.type === 'folder') {
          return updatedFolder;
        }
        if (entity.type === 'folder' && entity.children) {
          return {
            ...entity,
            children: updateFolderInData(entity.children),
          };
        }
        return entity;
      });

    setFolderData(updateFolderInData(folderData));
    setHasChanges(true);
    message.info(t('Changes will be saved when you click Save'));
  };

  const handleAddFolder = (newFolder: Entity) => {
    // Add new folder to data
    const addFolderToData = (entities: Entity[], folder: Entity): Entity[] => {
      if (folder.parent === '0') {
        return [...entities, folder];
      }

      return entities.map(entity => {
        if (
          entity.id.toString() === folder.parent &&
          entity.type === 'folder'
        ) {
          return {
            ...entity,
            children: [...(entity.children || []), folder],
          };
        }
        if (entity.type === 'folder' && entity.children) {
          return {
            ...entity,
            children: addFolderToData(entity.children, folder),
          };
        }
        return entity;
      });
    };

    setFolderData(addFolderToData(folderData, newFolder));
    setHasChanges(true);
    message.info(t('Changes will be saved when you click Save'));
  };

  const handleSave = () => {
    if (!hasChanges) {
      message.info(t('No changes to save'));
      return;
    }

    // Here you would implement the actual API calls to save all changes
    message.success(t('All changes saved successfully'));
    setHasChanges(false);
  };

  // Helper function to find entity by id
  // const findEntityById = (entities: Entity[], id: number): Entity | null => {
  //   for (const entity of entities) {
  //     if (entity.id === id) {
  //       return entity;
  //     }
  //     if (entity.type === 'folder' && entity.children) {
  //       const found = findEntityById(entity.children, id);
  //       if (found) return found;
  //     }
  //   }
  //   return null;
  // };

  const titleNode = canEdit ? (
    <span className="header-title">
      <Link to="/folder/0">
        <Icons.EditOutlined iconSize="l" />
      </Link>
      <span>{title}</span>
    </span>
  ) : (
    title
  );

  const extra = editMode ? (
    <ActionButtons
      onAddFolder={() => setAddFolderModalVisible(true)}
      onSave={handleSave}
      hasChanges={hasChanges}
    />
  ) : (
    <BrowseLink to={to} />
  );

  const treeData = buildTreeData(folderData, locale);

  const filteredTreeData = filterTreeData(treeData, searchTerm);

  return (
    <StyledCard title={titleNode} extra={extra}>
      <Input
        type="text"
        ref={searchInputRef as any}
        value={searchTerm}
        placeholder={t('Search')}
        onChange={changeSearch}
        prefix={
          <InputIconAlignment>
            <Icons.Search iconSize="m" />
          </InputIconAlignment>
        }
        suffix={
          <InputIconAlignment>
            {searchTerm && (
              <Icons.XLarge iconSize="m" onClick={stopSearching} />
            )}
          </InputIconAlignment>
        }
      />
      <div className="tree-container">
        <Tree
          treeData={filteredTreeData}
          titleRender={nodeData => {
            const { entity } = nodeData;
            if (isFolderType(entity)) {
              // const folderEntity = findEntityById(
              //   folderData,
              //   (nodeData as TreeNodeData).id,
              // );
              return (
                <FolderTitle
                  editMode={editMode}
                  folder={entity}
                  onEdit={() => setSelectedFolder(entity)}
                />
              );
            }

            return <DashboardTitle editMode={editMode} data={entity} />;
          }}
          switcherIcon={<Icons.CaretDownOutlined />}
          icon={(props: TreeNodeProps & TreeNodeData) => {
            if (props.data.entity.type === 'dashboard') {
              return <Icons.FundViewOutlined iconSize="m" />;
            }
            return props.expanded ? (
              <Icons.FolderOpenOutlined iconSize="m" />
            ) : (
              <Icons.FolderOutlined iconSize="m" />
            );
          }}
          selectable={editMode}
          draggable={editMode}
          onDragStart={onDragStart}
          onDragEnd={onDragEnd}
          onDrop={onDrop}
          allowDrop={allowDrop}
          className="tree"
          showLine={!editMode}
          showIcon
          defaultExpandAll
        />
      </div>

      {/* Modals */}
      <FolderModal
        mode="edit"
        show={Boolean(selectedFolder)}
        folder={selectedFolder}
        onCancel={() => setSelectedFolder(null)}
        onSuccess={handleEditFolder}
        onDelete={handleDeleteFolder}
      />

      <FolderModal
        mode="add"
        show={addFolderModalVisible}
        parentFolder={null}
        availableFolders={folderData}
        onCancel={() => setAddFolderModalVisible(false)}
        onSuccess={handleAddFolder}
      />
    </StyledCard>
  );
};

export default FolderCard;
