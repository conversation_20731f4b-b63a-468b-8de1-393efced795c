import { Entity, isFolderType, TreeNodeData } from './types';

export const buildTreeData = (
  entities: Entity[],
  locale: string,
): TreeNodeData[] =>
  entities.map(entity => ({
    key: `${entity.type}-${entity.id}`,
    title:
      locale === 'ru'
        ? entity.title_ru || entity.title_en
        : entity.title_en || entity.title_ru,
    entity,
    children: isFolderType(entity)
      ? buildTreeData(entity.children || [], locale)
      : [],
  }));

export const filterTreeData = (
  data: TreeNodeData[],
  searchTerm: string,
): TreeNodeData[] => {
  if (!searchTerm) return data;

  const matchesSearch = (node: TreeNodeData) =>
    node.entity.title_ru.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.entity.title_en.toLowerCase().includes(searchTerm.toLowerCase());

  const filterNode = (node: TreeNodeData): TreeNodeData | null => {
    if (isFolderType(node.entity) && matchesSearch(node)) {
      return {
        ...node,
      };
    }

    if (isFolderType(node.entity)) {
      const filteredChildren = node.children
        ?.map(filterNode)
        .filter(Boolean) as TreeNodeData[];

      if (filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren,
        };
      }
      return null;
    }

    if (matchesSearch(node)) {
      return node;
    }

    return null;
  };

  return data.map(filterNode).filter(Boolean) as TreeNodeData[];
};
