import { bootstrapData } from 'src/preamble';
import { css, t } from '@superset-ui/core';
import Icons from 'src/components/Icons';
import InfoTooltip from 'src/components/InfoTooltip';
import { Folder } from './types';

const locale = bootstrapData?.common?.locale || 'en';

const FolderTitle = ({
  editMode,
  onEdit,
  onDelete,
  folder,
}: {
  editMode: boolean;
  onEdit?: (folder: Folder) => void;
  onDelete?: (folder: Folder) => void;
  folder: Folder;
}) => {
  const title =
    locale === 'ru'
      ? folder.title_ru || folder.title_en
      : folder.title_en || folder.title_ru;

  const description =
    locale === 'ru'
      ? folder.description_ru || folder.description_en
      : folder.description_en || folder.description_ru;

  return editMode ? (
    <div className="item-title">
      <span
        css={css`
          display: flex;
          align-items: center;
          gap: 2px;
        `}
      >
        {title}
        {description && (
          <InfoTooltip
            tooltip={description}
            placement="top"
            iconStyle={{ fontSize: '20px' }}
          />
        )}
      </span>
      <div
        css={css`
          display: flex;
          align-items: center;
          gap: 8px;
        `}
      >
        <span
          role="button"
          tabIndex={0}
          title={t('Edit folder')}
          aria-label={t('Edit folder')}
          onClick={e => {
            e.stopPropagation();
            if (onEdit) {
              onEdit(folder);
            }
          }}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.stopPropagation();
              if (onEdit) {
                onEdit(folder);
              }
            }
          }}
        >
          <Icons.EditOutlined iconSize="m" />
        </span>
        <span
          role="button"
          tabIndex={0}
          title={t('Delete folder')}
          aria-label={t('Delete folder')}
          onClick={e => {
            e.stopPropagation();
            if (onDelete) {
              onDelete(folder);
            }
          }}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.stopPropagation();
              if (onDelete) {
                onDelete(folder);
              }
            }
          }}
          css={(theme: any) => css`
            color: ${theme.colors.error.base};
          `}
        >
          <Icons.DeleteOutlined iconSize="m" />
        </span>
      </div>
    </div>
  ) : (
    <span
      css={css`
        display: flex;
        align-items: center;
        gap: 2px;
      `}
    >
      {title}
      {description && (
        <InfoTooltip
          tooltip={description}
          placement="right"
          iconStyle={{ fontSize: '12px' }}
        />
      )}
    </span>
  );
};

export default FolderTitle;
