from flask_appbuilder import expose
from flask_appbuilder.security.decorators import has_access

from superset.superset_typing import FlaskResponse
from superset.views.base import BaseSupersetView
# from superset.views.base import permission_name


class FolderModelView(BaseSupersetView):
    route_base = "/folder"
    class_permission_name = "Folder"

    # TODO: как избавиться от этого url? без него не заводится
    @expose("/list")
    def list(self) -> FlaskResponse:
        return super().render_app_template()

    @expose("/<int:pk>/")
    # @has_access
    # @permission_name("read")
    def get(self, pk: int) -> FlaskResponse:  # pylint: disable=unused-argument
        return super().render_app_template()
